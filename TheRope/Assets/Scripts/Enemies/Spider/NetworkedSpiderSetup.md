# Networked Spider Controller Setup Guide

## Overview

The SpiderController has been modified to work with PUN2 networking, ensuring that the spider's state and target are synchronized across all clients in the game.

## Key Changes Made

### 1. Class Inheritance
- Changed from `MonoStateMachine` to `PUN2Behaviour, IPunObservable`
- Added manual state machine implementation since we no longer inherit from MonoStateMachine

### 2. Networking Fields Added
```csharp
private EnemyEnum _networkCurrentState = EnemyEnum.Idle;
private int _networkTargetViewID = -1; // PhotonView ID of the target
private Vector3 _networkTargetPosition = Vector3.zero; // Fallback position if target has no PhotonView
```

### 3. Master Client Control
- Only the master client (photonView.IsMine) controls the spider's AI logic
- Other clients receive and apply state/target changes via networking

### 4. State Synchronization
- State changes are sent via RPC events to all clients
- Uses `OnPhotonSerializeView` for continuous synchronization
- Implements `HandleObjectEvent` for handling network events

### 5. Target Synchronization
- Supports both networked targets (with PhotonView) and static targets (patrol points)
- Uses PhotonView ID for networked objects, position fallback for static objects

## Setup Instructions

### 1. PhotonView Component
Add a PhotonView component to your Spider GameObject with the following settings:
- **Ownership**: Master Client
- **Observed Components**: Add the SpiderController script
- **Synchronization**: Send Rate and Serialization Rate as needed (default values should work)

### 2. Network Manager
Ensure your scene has the PUN2NetworkManager properly configured and that the spider is instantiated through PhotonNetwork.Instantiate() or is present in the scene with proper PhotonView setup.

### 3. Player Targets
Make sure player GameObjects have PhotonView components so they can be properly synchronized as targets.

## How It Works

### State Management
1. **Master Client**: Runs the AI logic and makes state decisions
2. **State Changes**: When state changes, master client calls `ChangeStateLocal()`
3. **Network Sync**: State change is sent to all clients via RPC
4. **Client Update**: Non-master clients receive and apply the state change

### Target Management
1. **Target Setting**: Master client sets targets via `SetTargetLocal()`
2. **Network Sync**: Target info (PhotonView ID or position) is sent to all clients
3. **Target Resolution**: Clients resolve the target using PhotonView.Find() or position matching

### Continuous Sync
- `OnPhotonSerializeView` continuously syncs current state and target info
- Provides redundancy and handles late-joining clients
- Ensures all clients stay synchronized even if RPC events are missed

## Usage Examples

### Changing State (from AI logic)
```csharp
// This will automatically sync to all clients
spiderController.ChangeState(SpiderController.EnemyEnum.Chase);
```

### Setting Target (from AI logic)
```csharp
// This will automatically sync to all clients
spiderController.SetTarget(playerTransform);
```

## Important Notes

1. **Master Client Only**: Only the master client should make AI decisions. Other clients will automatically receive and apply changes.

2. **PhotonView Required**: The Spider GameObject must have a PhotonView component for networking to work.

3. **Player Synchronization**: Player targets should have PhotonView components for proper synchronization.

4. **Patrol Points**: Static patrol points are synchronized by position matching, which should work fine for static objects.

5. **Performance**: The networking adds minimal overhead and uses PUN2's built-in optimization features.

## Troubleshooting

### Spider Not Moving on Clients
- Check that the Spider has a PhotonView component
- Verify that the PhotonView ownership is set to Master Client
- Ensure the SpiderController is added to the PhotonView's Observed Components

### State Not Syncing
- Check that the PUN2NetworkManager is properly initialized
- Verify that the spider is instantiated correctly in the network
- Check the console for any RPC or networking errors

### Target Not Syncing
- Ensure player targets have PhotonView components
- Check that patrol points are properly set up and accessible to all clients
- Verify that the target resolution logic is working correctly

## Testing

1. **Single Player**: Should work exactly as before
2. **Multiplayer**: Test with multiple clients to ensure state and target sync
3. **Master Client Switch**: Test what happens when the master client disconnects (PUN2 should handle this automatically)
